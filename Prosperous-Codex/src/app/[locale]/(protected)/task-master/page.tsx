"use client";

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { useRouter } from '@/i18n/navigation';
import { useTranslations } from 'next-intl';
import { useSession } from 'next-auth/react';
import { ArrowLeft, Filter, CalendarIcon, X, Sparkles, Send, GripVertical, RefreshCw, Brain, FileText, Zap } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { IconButton } from '@/components/ui/icon-button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { TaskMasterColumn } from '@/components/task-master/task-master-column';
import { TaskMaster } from '@/lib/types/task-master';
import { Calendar } from '@/components/ui/calendar';

import ProjectFlowBoard from '@/components/task-master/project-flow-board';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { useTaskMaster } from '@/hooks/use-task-master';
import { useDragState } from '@/hooks/use-drag-state';
import { useColumnHeightSync } from '@/hooks/use-column-height-sync';
import { useEnhancedCardAnimation } from '@/hooks/use-enhanced-card-animation';
import { useToast } from '@/hooks/use-toast';
import { TrashBin } from '@/components/task-master/trash-bin';
import { DeleteConfirmationDialog } from '@/components/task-master/delete-confirmation-dialog';
import { CardAnimationOverlay } from '@/components/task-master/card-animation-overlay';
import ConfettiExplosion from 'react-confetti-explosion';

export default function TaskMasterPage() {
  const router = useRouter();
  const t = useTranslations('taskMaster');
  const { data: session } = useSession();
  const { toast } = useToast();
  const [date, setDate] = useState<Date | undefined>(new Date());

  // Column height synchronization
  const { synchronizedHeight, registerColumnRef, recalculateHeight } = useColumnHeightSync({
    minHeight: 600,
    debounceMs: 150
  });

  // Backend integration
  const {
    taskColumns,
    isLoading,
    error,
    fetchProjects,
    refreshProjects,
    invalidateCache,
    createProject,
    updateProject,
    moveTask,
    deleteProject,
    createTask,
    updateTask,
    deleteTask,
    handleFileUploaded,
    handleFileDeleted,
    handleTeamUpdated
  } = useTaskMaster();

  // Drag state management for task deletion
  const { isDragging: isTaskDragging, startDrag, endDrag } = useDragState();

  // Enhanced card animation system
  const { startAnimation, currentAnimation, animationConfig } = useEnhancedCardAnimation({
    duration: 400,
    easing: 'cubic-bezier(0.4, 0.0, 0.2, 1)'
  });

  // Delete confirmation dialog state
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [taskToDelete, setTaskToDelete] = useState<TaskMaster | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Confetti animation state
  const [confettiConfig, setConfettiConfig] = useState<{
    isExploding: boolean;
    position: { x: number; y: number };
  }>({ isExploding: false, position: { x: 0, y: 0 } });

  // Handle delete task from trash bin
  const handleDeleteTask = (taskId: string) => {
    // Find the task to delete
    const task = taskColumns.flatMap(col => col.tasks).find(t => t.id === taskId);
    if (task) {
      setTaskToDelete(task);
      setDeleteDialogOpen(true);
    }
  };

  // Confirm deletion
  const handleConfirmDelete = async () => {
    if (!taskToDelete) return;

    setIsDeleting(true);
    try {
      await deleteProject(taskToDelete.id);
      setDeleteDialogOpen(false);
      setTaskToDelete(null);
      // Trigger height recalculation after task deletion
      setTimeout(() => recalculateHeight(), 100);
    } catch (error) {
      // Error handling is done in the deleteProject function
      console.error('Failed to delete task:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  // Cancel deletion
  const handleCancelDelete = () => {
    setDeleteDialogOpen(false);
    setTaskToDelete(null);
  };

  // Project Flow Board state
  const [isProjectFlowBoardOpen, setIsProjectFlowBoardOpen] = useState(false);
  const [selectedTask, setSelectedTask] = useState<TaskMaster | null>(null);

  // Project Details Modal state
  const [isProjectDetailsModalOpen, setIsProjectDetailsModalOpen] = useState(false);
  const [projectDescription, setProjectDescription] = useState('');
  const [tempProjectDescription, setTempProjectDescription] = useState('');
  const optimisticUpdateRef = useRef<((description: string) => void) | null>(null);

  // Parsing state
  const [isParsing, setIsParsing] = useState(false);
  const parseContentRef = useRef<((content: string) => Promise<void>) | null>(null);

  // Frontend authorization check for project description editing
  const canEditProjectDescription = useMemo(() => {
    if (!session?.user?.id || !selectedTask) return false;
    return selectedTask.createdBy?.toString() === session.user.id;
  }, [session?.user?.id, selectedTask?.createdBy]);

  // Listen for modal description updates from parsing
  useEffect(() => {
    const handleModalDescriptionUpdate = (event: CustomEvent) => {
      const { description } = event.detail;
      setTempProjectDescription(description);
    };

    window.addEventListener('updateModalDescription', handleModalDescriptionUpdate as EventListener);

    return () => {
      window.removeEventListener('updateModalDescription', handleModalDescriptionUpdate as EventListener);
    };
  }, []);

  // New Task Creation state
  const [newTaskInput, setNewTaskInput] = useState('');
  const [textareaHeight, setTextareaHeight] = useState(40); // Initial height in pixels (1 line)
  const [isResizing, setIsResizing] = useState(false);
  const [dragStart, setDragStart] = useState<{ y: number; height: number } | null>(null);
  const [isFocused, setIsFocused] = useState(false);
  const [hasManualResize, setHasManualResize] = useState(false);

  // Enhanced function to move tasks between columns with improved animation
  const handleMoveTask = async (taskId: string, fromColumnId: string, toColumnId: string) => {
    // Find the task data
    const task = taskColumns.flatMap(col => col.tasks).find(t => t.id === taskId);
    if (!task) return;

    // Get source card element and position before move
    const sourceCardElement = document.querySelector(`[data-task-card="${taskId}"]`) as HTMLElement;
    if (!sourceCardElement) {
      // Fallback to immediate move if source not found
      await moveTask(taskId, fromColumnId, toColumnId);
      setTimeout(() => recalculateHeight(), 100);
      return;
    }

    // Store source position before DOM changes
    const sourceRect = sourceCardElement.getBoundingClientRect();

    // Step 1: Execute the move (system handles positioning with filters/sorting)
    await moveTask(taskId, fromColumnId, toColumnId);

    // Step 2: Wait for DOM update to complete
    await new Promise(resolve => setTimeout(resolve, 50));

    // Step 3: Find the card in its new position
    const newCardElement = document.querySelector(`[data-task-card="${taskId}"]`) as HTMLElement;
    if (!newCardElement) {
      // If card not found in new position, just recalculate height
      setTimeout(() => recalculateHeight(), 100);
      return;
    }

    // Step 4: Hide the card in its new position temporarily
    const originalVisibility = newCardElement.style.visibility;
    newCardElement.style.visibility = 'hidden';

    // Step 5: Get the actual destination position
    const destinationRect = newCardElement.getBoundingClientRect();

    // Step 6: Start animation from source to actual destination
    startAnimation(
      taskId,
      task,
      sourceRect,
      destinationRect,
      fromColumnId,
      toColumnId,
      () => {
        // Step 7: Show the card in its final position after animation
        newCardElement.style.visibility = originalVisibility;

        // Trigger confetti AFTER animation completes if moving to completed column
        if (toColumnId === 'completed' && fromColumnId !== 'completed') {
          // Use the destination position (where the card ended up)
          const centerX = destinationRect.left + destinationRect.width / 2;
          const centerY = destinationRect.top + destinationRect.height / 2;

          setConfettiConfig({
            isExploding: true,
            position: { x: centerX, y: centerY }
          });

          // Reset confetti after animation duration
          setTimeout(() => {
            setConfettiConfig(prev => ({ ...prev, isExploding: false }));
          }, 2600);
        }

        setTimeout(() => recalculateHeight(), 100);
      }
    );
  };

  // Trigger height recalculation when task columns change
  useEffect(() => {
    recalculateHeight();
  }, [taskColumns, recalculateHeight]);

  const totalActiveTasks = taskColumns.reduce((sum, column) => {
    if (column.id !== 'completed') {
      return sum + column.tasks.length;
    }
    return sum;
  }, 0);

  const handleBackToDashboard = () => {
    router.push('/dashboard');
  };

  const handleTaskClick = (task: TaskMaster) => {
    setSelectedTask(task);
    setProjectDescription(task?.projectDetails?.fullDescription || task?.details || '');
    setIsProjectFlowBoardOpen(true);
  };

  const handleProjectDetailsModalOpen = (description: string) => {
    setTempProjectDescription(description);
    setIsProjectDetailsModalOpen(true);
  };

  const handleSaveProjectDetails = async () => {
    if (!selectedTask) return;

    // FIXED: Use temporary state management instead of immediate API save
    // Update the drawer's temporary state and trigger change detection
    if (optimisticUpdateRef && optimisticUpdateRef.current) {
      optimisticUpdateRef.current(tempProjectDescription);
    }

    // Store previous description for potential rollback
    const previousDescription = projectDescription;

    // FIXED: Use temporary state management - no immediate API calls
    // Update local state for immediate UI feedback
    setProjectDescription(tempProjectDescription);
    setIsProjectDetailsModalOpen(false);

    // Trigger change detection in the drawer to require confirmation
    // This will be handled by the drawer's batch submission system
    console.log('✅ Project description updated in modal (temporary state)');
  };

  const handleCancelProjectDetails = () => {
    setTempProjectDescription('');
    setIsProjectDetailsModalOpen(false);
  };

  // Parse content handler
  const handleParseContent = async () => {
    if (!tempProjectDescription.trim() || !parseContentRef.current) return;

    setIsParsing(true);
    try {
      await parseContentRef.current(tempProjectDescription);
      // DO NOT close modal - let user review parsed content and manually save
    } catch (error) {
      // Error handling is done in ProjectFlowBoard
    } finally {
      setIsParsing(false);
    }
  };

  // Optimistic status update handler with animations
  const handleOptimisticStatusUpdate = useCallback(async (taskId: string, newStatus: string) => {
    // Invalidate cache to ensure fresh data on next fetch
    invalidateCache();

    // Use handleMoveTask for consistent animations
    const statusToColumnMap = {
      'todo': 'todo',
      'inProgress': 'inProgress',
      'completed': 'completed'
    };

    // Find the current task and its column
    let currentTask: TaskMaster | null = null;
    let currentColumnId: string | null = null;

    for (const column of taskColumns) {
      const task = column.tasks.find(t => t.id === taskId);
      if (task) {
        currentTask = task;
        currentColumnId = column.id;
        break;
      }
    }

    if (currentTask && currentColumnId) {
      const targetColumnId = statusToColumnMap[newStatus as keyof typeof statusToColumnMap];
      if (targetColumnId && targetColumnId !== currentColumnId) {
        await handleMoveTask(taskId, currentColumnId, targetColumnId);
      }
    }
  }, [taskColumns, handleMoveTask, invalidateCache]);

  // New Task Creation handlers
  const handleNewTaskInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setNewTaskInput(newValue);

    // Clear thinking content when user manually edits the input significantly
    // Only clear if the content has changed substantially (not just minor edits)
    if (aiThinkingContent && newValue.length < newTaskInput.length * 0.8) {
      setAiThinkingContent(null);
      setShowThinking(false);
      // Reset height to auto-adjust when thinking content is cleared, but respect content size
      if (!hasManualResize) {
        const lines = newValue.split('\n').length;
        // Don't shrink below current content needs, but allow natural sizing
        const estimatedHeight = Math.min(480, Math.max(40, lines * 20 + 40));
        setTextareaHeight(estimatedHeight);
      }
    }
  };

  const handleTextareaFocus = () => {
    setIsFocused(true);
    // Only auto-expand if user hasn't manually resized AND the field is empty or very small
    // Don't auto-expand if there's already content (like AI-enhanced text) as that would shrink it
    if (!hasManualResize && newTaskInput.trim().length === 0) {
      setTextareaHeight(72); // 3 lines - only for empty fields
    }
  };

  const handleTextareaBlur = () => {
    setIsFocused(false);
    // Only reset to 1 line if there's no AI thinking content, no manual resize, AND the field is empty
    // This prevents auto-collapse after AI enhancement or when there's content
    if (!aiThinkingContent && !hasManualResize && newTaskInput.trim().length === 0) {
      setTextareaHeight(40); // 1 line - only for empty fields
    }
  };

  const [isEnhancing, setIsEnhancing] = useState(false);

  // AI thinking content state
  const [aiThinkingContent, setAiThinkingContent] = useState<string | null>(null);
  const [showThinking, setShowThinking] = useState(false);

  // Turbo mode state
  const [isTurboProcessing, setIsTurboProcessing] = useState(false);
  const [turboProcessingCardId, setTurboProcessingCardId] = useState<string | null>(null);
  const [turboProgressText, setTurboProgressText] = useState('');
  const [turboAttempt, setTurboAttempt] = useState(0);

  // AbortController for cancelling pending API calls
  const abortControllerRef = useRef<AbortController | null>(null);

  // Turbo state reset functions
  const resetTurboState = useCallback(() => {
    setIsTurboProcessing(false);
    setTurboProcessingCardId(null);
    setTurboProgressText('');
    setTurboAttempt(0);
  }, []);

  // Cancel pending turbo operations
  const cancelTurboOperation = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    resetTurboState();
  }, [resetTurboState]);

  // Cleanup on component unmount and navigation
  useEffect(() => {
    const handleBeforeUnload = () => {
      cancelTurboOperation();
    };

    // Add event listener for page unload
    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      // Cleanup on unmount
      cancelTurboOperation();
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [cancelTurboOperation]);

  // Function to parse AI response and extract thinking content using buffer-based approach
  // This properly handles cases where AI mentions </thinking> within its reasoning process
  const parseAIResponse = (response: string) => {
    const thinkingStartTag = '<thinking>';
    const thinkingEndTag = '</thinking>';

    const firstThinkingStart = response.indexOf(thinkingStartTag);
    const lastThinkingEnd = response.lastIndexOf(thinkingEndTag);

    if (firstThinkingStart !== -1 && lastThinkingEnd !== -1 && lastThinkingEnd > firstThinkingStart) {
      // Extract thinking content between first <thinking> and last </thinking>
      const thinkingStart = firstThinkingStart + thinkingStartTag.length;
      const thinkingContent = response.substring(thinkingStart, lastThinkingEnd).trim();

      // Extract final response after the last </thinking>
      const finalStart = lastThinkingEnd + thinkingEndTag.length;
      const finalResponse = response.substring(finalStart).trim();

      return { thinkingContent, finalResponse };
    }

    return { thinkingContent: null, finalResponse: response.trim() };
  };

  const handleAIEnhance = async () => {
    if (!newTaskInput.trim() || isEnhancing) return;

    setIsEnhancing(true);

    try {
      const response = await fetch('/api/task-master/ai-enhance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          inputText: newTaskInput.trim(),
          context: 'task_description',
          tone: 'professional'
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to enhance text');
      }

      const data = await response.json();

      if (data.success && data.data.enhancedText) {
        // Parse the AI response to extract thinking content and final response
        const { thinkingContent, finalResponse } = parseAIResponse(data.data.enhancedText);

        // Store thinking content if it exists
        setAiThinkingContent(thinkingContent);

        // Set the final response (without thinking tags) as the input
        setNewTaskInput(finalResponse);

        // Auto-expand textarea for enhanced content (reduced max height from 800px to 480px)
        const lines = finalResponse.split('\n').length;
        const estimatedHeight = Math.min(480, Math.max(120, lines * 20 + 40)); // Dynamic height based on content
        setTextareaHeight(estimatedHeight);
        setHasManualResize(false); // Allow auto-expansion for enhanced content

        // Recalculate height for the enhanced text
        setTimeout(() => recalculateHeight(), 100);

        // Success feedback is provided by the visual update of the textarea content
        // No toast notification needed as users can see the enhancement result
      } else {
        throw new Error('Invalid response format');
      }

    } catch (error) {
      console.error('AI Enhancement Error:', error);

      toast({
        title: "Enhancement Failed",
        description: error instanceof Error ? error.message : "Failed to enhance text. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsEnhancing(false);
    }
  };

  // Helper function to determine if an error should trigger a retry
  const shouldRetryTurboError = (error: Error): boolean => {
    const errorMessage = error.message.toLowerCase();

    // Retryable errors
    if (errorMessage.includes('rate limit')) return true;
    if (errorMessage.includes('timeout')) return true;
    if (errorMessage.includes('network')) return true;
    if (errorMessage.includes('fetch')) return true;
    if (errorMessage.includes('503')) return true; // Service unavailable
    if (errorMessage.includes('502')) return true; // Bad gateway
    if (errorMessage.includes('504')) return true; // Gateway timeout

    // Non-retryable errors
    if (errorMessage.includes('401')) return false; // Auth error
    if (errorMessage.includes('403')) return false; // Forbidden
    if (errorMessage.includes('400')) return false; // Bad request
    if (errorMessage.includes('content filter')) return false; // Content policy
    if (errorMessage.includes('invalid response format')) return false; // Parse error
    if (errorMessage.includes('incomplete data')) return false; // Data validation error

    // Default to non-retryable for unknown errors
    return false;
  };

  // Turbo API call helper function
  const callTurboAPI = async (inputText: string, abortSignal?: AbortSignal) => {
    const response = await fetch('/api/task-master/ai-turbo', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        inputText: inputText,
        context: 'task_description',
        tone: 'professional'
      }),
      signal: abortSignal, // Add abort signal support
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();

    if (!data.success || !data.data) {
      throw new Error('Invalid response format from Turbo API');
    }

    // Validate required fields
    const result = data.data;
    if (!result.title || !result.description) {
      throw new Error('Incomplete data received from Turbo API');
    }

    return {
      title: result.title,
      description: result.description,
      eventLog: result.eventLog || '',
      tasks: result.tasks || [],
      enhancedText: result.enhancedText,
      reasoningContent: result.reasoningContent,
      modelUsed: result.modelUsed,
      tokensUsed: result.tokensUsed
    };
  };

  // Handle turbo failure with user-friendly messages and graceful degradation
  const handleTurboFailure = (error: Error, createdProject: any | null, wasRetried: boolean, attemptCount: number) => {
    console.error('Turbo Mode Error:', error);

    const errorMessage = error.message.toLowerCase();
    let title = "Turbo Mode Failed";
    let description = "";

    // Classify error types for user-friendly messages
    if (errorMessage.includes('rate limit')) {
      title = "Rate Limit Exceeded";
      description = "Too many requests. Please wait a moment and try again.";
    } else if (errorMessage.includes('auth') || errorMessage.includes('401')) {
      title = "Authentication Error";
      description = "AI service authentication failed. Please check your configuration.";
    } else if (errorMessage.includes('content filter') || errorMessage.includes('safety')) {
      title = "Content Policy Violation";
      description = "Content cannot be processed due to safety guidelines. Please modify your input.";
    } else if (errorMessage.includes('timeout') || errorMessage.includes('network')) {
      title = "Connection Error";
      description = wasRetried
        ? `Network issues persist after ${attemptCount} attempts. Please try again later.`
        : "Network timeout occurred. Please check your connection and try again.";
    } else if (errorMessage.includes('503') || errorMessage.includes('service unavailable')) {
      title = "Service Unavailable";
      description = "AI service is temporarily unavailable. Please try again later.";
    } else if (errorMessage.includes('invalid response') || errorMessage.includes('parse')) {
      title = "Processing Error";
      description = "AI response could not be processed. Please try again.";
    } else {
      // Generic error with retry information
      description = wasRetried
        ? `Failed after ${attemptCount} attempts: ${error.message}`
        : error.message;
    }

    // Show toast notification
    toast({
      title,
      description,
      variant: "destructive"
    });

    // If a project was created, inform user they can manually enhance/parse it
    if (createdProject) {
      setTimeout(() => {
        toast({
          title: "Project Created",
          description: "A basic project was created. You can manually enhance and parse it using the individual buttons.",
          variant: "default"
        });
      }, 2000); // Show after error toast
    }
  };

  // Input validation and sanitization for Turbo mode
  const validateTurboInput = (input: string): { isValid: boolean; error?: string; sanitized?: string } => {
    // Basic validation
    if (!input || !input.trim()) {
      return { isValid: false, error: 'Input text is required' };
    }

    const trimmed = input.trim();

    // Length validation
    const minLength = 10;
    const maxLength = 65536; // Match API limit

    if (trimmed.length < minLength) {
      return { isValid: false, error: `Input too short (minimum ${minLength} characters)` };
    }

    if (trimmed.length > maxLength) {
      return { isValid: false, error: `Input too long (maximum ${maxLength.toLocaleString()} characters)` };
    }

    // Content validation - check for potentially problematic content
    const suspiciousPatterns = [
      /^\s*test\s*$/i,
      /^\s*hello\s*$/i,
      /^\s*hi\s*$/i,
      /^\s*[a-z]\s*$/i, // Single letter
    ];

    if (suspiciousPatterns.some(pattern => pattern.test(trimmed))) {
      return {
        isValid: false,
        error: 'Please provide a more detailed description for better AI processing'
      };
    }

    // Basic sanitization - remove excessive whitespace and normalize
    const sanitized = trimmed
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .replace(/\n\s*\n\s*\n/g, '\n\n') // Limit consecutive newlines to 2
      .trim();

    return { isValid: true, sanitized };
  };

  // Turbo mode handler - combines enhancement and parsing in one operation
  const handleTurboMode = async () => {
    // Input validation and sanitization
    const validation = validateTurboInput(newTaskInput);
    if (!validation.isValid) {
      toast({
        title: "Invalid Input",
        description: validation.error,
        variant: "destructive"
      });
      return;
    }

    const sanitizedInput = validation.sanitized!;
    let createdProject: any = null;
    const maxAttempts = 2;

    try {
      // Create AbortController for this operation
      abortControllerRef.current = new AbortController();

      setTurboAttempt(1);
      setTurboProgressText('Creating project...');
      setIsTurboProcessing(true);

      // Step 1: Create real card using existing createProject() function
      const projectData = {
        title: 'New Task', // Placeholder title that will be replaced with Task #[ID]
        fullDescription: sanitizedInput,
        status: 'todo' as const,
        priority: 'medium' as const,
        progress: 0
      };

      createdProject = await createProject(projectData);
      if (!createdProject) {
        throw new Error('Failed to create project');
      }

      // Step 2: Set turbo processing state with the created card ID
      setTurboProcessingCardId(createdProject.id.toString());

      // Step 3: Call turbo API endpoint with retry logic
      let turboResult;
      let lastError: Error | null = null;

      for (let attempt = 1; attempt <= maxAttempts; attempt++) {
        try {
          setTurboAttempt(attempt);
          setTurboProgressText(attempt === 1 ? 'Processing with AI...' : `Retrying... (${attempt}/${maxAttempts})`);

          turboResult = await callTurboAPI(sanitizedInput, abortControllerRef.current?.signal);
          break; // Success, exit retry loop

        } catch (apiError) {
          lastError = apiError instanceof Error ? apiError : new Error('Unknown API error');

          // If this is the last attempt or error is not retryable, throw
          if (attempt === maxAttempts || !shouldRetryTurboError(lastError)) {
            throw lastError;
          }

          // Wait before retry (exponential backoff: 1s, 2s)
          await new Promise(resolve => setTimeout(resolve, attempt * 1000));
        }
      }

      if (!turboResult) {
        throw lastError || new Error('Failed to get turbo result after retries');
      }

      // FULL TURBO MODE: Update project card AND populate textarea for inspection
      setTurboProgressText('Updating project and populating textarea...');

      // Step 4A: Update existing card using updateProject() function
      const updateSuccess = await updateProject(createdProject.id, {
        title: turboResult.title,
        fullDescription: turboResult.description,
        eventLog: turboResult.eventLog
      });

      if (!updateSuccess) {
        throw new Error('Failed to update project with AI data');
      }

      // Step 4B: Create tasks if they exist in the turbo result
      if (turboResult.tasks && turboResult.tasks.length > 0) {
        setTurboProgressText('Creating AI-generated tasks...');

        try {
          for (const task of turboResult.tasks) {
            // Create main task
            const createdTask = await createTask(parseInt(createdProject.id), {
              title: task.title,
              description: task.description,
              status: 'todo' as const,
              priority: 'medium' as const
            });

            // Create subtasks if they exist
            if (task.subtasks && task.subtasks.length > 0 && createdTask) {
              for (const subtask of task.subtasks) {
                await createTask(parseInt(createdProject.id), {
                  title: `${subtask.id}: ${subtask.description}`,
                  description: subtask.description,
                  status: 'todo' as const,
                  priority: 'medium' as const,
                  parentTaskId: createdTask.id
                });
              }
            }
          }
        } catch (taskError) {
          console.warn('Failed to create some tasks:', taskError);
          // Don't fail the entire operation if task creation fails
        }
      }

      // Step 4C: Also populate the textarea with enhanced text for inspection
      setTurboProgressText('Finalizing...');
      setNewTaskInput(turboResult.enhancedText || turboResult.description || 'Enhanced content generated');
      setAiThinkingContent(turboResult.reasoningContent || null);
      setShowThinking(false);

      // Recalculate textarea height for the new content
      setHasManualResize(false);
      setTextareaHeight(40);
      setTimeout(() => recalculateHeight(), 100);

      // Success feedback for full Turbo mode
      const hasThinkingContent = turboResult.reasoningContent && turboResult.reasoningContent.trim().length > 0;
      toast({
        title: "Turbo Mode Complete",
        description: hasThinkingContent
          ? "Project created and populated with AI-enhanced content. Enhanced text also available in textarea for inspection. Click the brain icon to view AI reasoning."
          : "Project created and populated with AI-enhanced content. Enhanced text also available in textarea for inspection.",
        variant: "default"
      });

      // If there's thinking content, briefly show the thinking toggle hint
      if (hasThinkingContent) {
        setTimeout(() => {
          setShowThinking(true);
          setTimeout(() => setShowThinking(false), 3000);
        }, 1000);
      }

    } catch (error) {
      // Handle abort errors gracefully
      if (error instanceof Error && error.name === 'AbortError') {
        return; // Don't show error toast for user-initiated cancellations
      }

      // Use enhanced error handling with graceful degradation
      const errorObj = error instanceof Error ? error : new Error("Unknown error occurred");
      const wasRetried = turboAttempt > 1;

      handleTurboFailure(errorObj, createdProject, wasRetried, turboAttempt);
    } finally {
      // Clean up AbortController
      if (abortControllerRef.current) {
        abortControllerRef.current = null;
      }
      resetTurboState();
    }
  };

  const handleSubmitNewTask = async () => {
    if (newTaskInput.trim()) {
      const success = await createProject({
        title: 'New Task', // Placeholder title that will be replaced with Task #[ID]
        fullDescription: newTaskInput.trim(), // Input now becomes the description
        status: 'todo',
        priority: 'medium',
        progress: 0
      });

      if (success) {
        setNewTaskInput(''); // Clear input after submission
        // Clear AI thinking content and reset all related state
        setAiThinkingContent(null);
        setShowThinking(false);
        // Reset height and manual resize flag after submission
        setHasManualResize(false);
        setTextareaHeight(40); // Back to 1 line
        // Trigger height recalculation after new task creation
        setTimeout(() => recalculateHeight(), 100);
      }
    }
  };

  // Resize handle drag functionality
  const handleResizeMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsResizing(true);
    setDragStart({
      y: e.clientY,
      height: textareaHeight
    });
  };

  // Mouse move and up handlers for resizing
  React.useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (isResizing && dragStart) {
        const deltaY = dragStart.y - e.clientY; // Inverted because we want dragging up to increase height
        const newHeight = Math.max(64, Math.min(480, dragStart.height + deltaY)); // Min 4 lines (64px), Max 30 lines (480px)
        setTextareaHeight(newHeight);
        setHasManualResize(true); // Mark that user has manually resized
      }
    };

    const handleMouseUp = () => {
      setIsResizing(false);
      setDragStart(null);
    };

    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isResizing, dragStart, textareaHeight]);

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="flex h-full w-full flex-col items-start -m-6">
      {/* Header */}
      <div className="flex w-full flex-wrap items-center justify-between border-b border-gray-200 dark:border-[#2A2A2A] px-6 py-6 bg-white dark:bg-[#1A1A1A]">
        <div className="flex items-center gap-3">
          <IconButton
            size="small"
            variant="neutral-tertiary"
            icon={<ArrowLeft className="h-4 w-4" />}
            onClick={handleBackToDashboard}
          />
          <h1 className="text-xl font-semibold text-foreground">
            {t('header.title')}
          </h1>
          <Badge variant="secondary" className="bg-[#5E6AD2]/10 text-[#5E6AD2] dark:bg-[#6E56CF]/10 dark:text-[#6E56CF]">
            {totalActiveTasks} {t('header.active')}
          </Badge>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="neutral-secondary"
            onClick={() => refreshProjects()}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            {t('header.refresh')}
          </Button>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                className="w-auto flex-none"
                variant="brand-secondary"
              >
                <CalendarIcon className="h-4 w-4" />
                {t('header.calendar')}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="center">
              <Calendar
                mode="single"
                selected={date}
                onSelect={setDate}
              />
            </PopoverContent>
          </Popover>
          <Button
            variant="neutral-secondary"
            onClick={() => {}}
          >
            <Filter className="h-4 w-4" />
            {t('header.filter')}
          </Button>
        </div>
      </div>

      {/* Content - Three Column Layout */}
      <div className="flex w-full items-start px-6 py-6 pb-28 min-w-fit">
        {isLoading && taskColumns.length === 0 ? (
          <div className="flex w-full justify-center items-center py-20">
            <div className="flex flex-col items-center gap-4">
              <RefreshCw className="h-8 w-8 animate-spin text-[#5E6AD2] dark:text-[#6E56CF]" />
              <p className="text-gray-600 dark:text-gray-400">{t('status.loading')}</p>
            </div>
          </div>
        ) : error ? (
          <div className="flex w-full justify-center items-center py-20">
            <div className="flex flex-col items-center gap-4">
              <p className="text-red-600 dark:text-red-400">{t('status.error')}: {error}</p>
              <Button onClick={() => refreshProjects()} variant="outline">
                {t('status.tryAgain')}
              </Button>
            </div>
          </div>
        ) : (
          taskColumns.map((column, index) => (
            <React.Fragment key={column.id}>
              <div data-column-id={column.id}>
                <TaskMasterColumn
                  column={column}
                  onTaskClick={handleTaskClick}
                  onMoveTask={handleMoveTask}
                  onDragStart={startDrag}
                  onDragEnd={endDrag}
                  synchronizedHeight={synchronizedHeight}
                  columnRef={registerColumnRef(index)}
                  animatingCardIds={new Set(currentAnimation ? [currentAnimation.cardId] : [])}
                  isTurboProcessing={isTurboProcessing}
                  turboProcessingCardId={turboProcessingCardId}
                  turboProgressText={turboProgressText}
                />
              </div>
              {index < taskColumns.length - 1 && (
                <div
                  className="w-px bg-gray-200 dark:bg-[#2A2A2A] mx-6 flex-none"
                  style={{ minHeight: `${synchronizedHeight}px` }}
                />
              )}
            </React.Fragment>
          ))
        )}
      </div>

      {/* Fixed Bottom New Task Creation Section - Floating */}
      <div className="fixed bottom-6 left-6 right-6 z-[300]">
        <div className="relative w-[65%] max-w-7xl mx-auto mr-[15%]">
          {/* Resize Handle */}
          <div
            className={`absolute -top-2 left-1/2 transform -translate-x-1/2 w-12 h-4 flex items-center justify-center cursor-ns-resize hover:bg-[#5E6AD2]/10 dark:hover:bg-[#6E56CF]/10 rounded-t-md transition-colors duration-200 ${isResizing ? 'bg-[#5E6AD2]/20 dark:bg-[#6E56CF]/20' : ''}`}
            onMouseDown={handleResizeMouseDown}
            title={t('actions.dragResize')}
          >
            <GripVertical className="h-3 w-3 text-[#5E6AD2] dark:text-[#6E56CF] rotate-90" />
          </div>

          <div className="relative w-full">
            {/* Side-by-side layout when thinking content is shown */}
            {aiThinkingContent && showThinking ? (
              <div className="flex flex-col lg:flex-row gap-4 transition-all duration-300">
                {/* Left side: AI Thinking Content (Read-only) */}
                <div className="w-full lg:flex-1 lg:w-auto">
                  {/* External label for AI Reasoning */}
                  <div className="flex items-center gap-2 mb-2 text-sm font-medium text-gray-500 dark:text-gray-400">
                    <Brain className="h-4 w-4" />
                    <span>AI Reasoning Process</span>
                    <span className="text-xs text-gray-400 dark:text-gray-500">(Read-only)</span>
                  </div>
                  <div className="relative">
                    <textarea
                      value={aiThinkingContent}
                      readOnly
                      style={{
                        height: `${textareaHeight}px`,
                        transition: hasManualResize ? 'none' : 'height 0.2s ease-in-out'
                      }}
                      className="w-full px-4 py-3 text-sm rounded-md bg-gray-50 dark:bg-gray-900/70 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700 focus:outline-none resize-none overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent shadow-sm cursor-default"
                    />
                  </div>
                </div>

                {/* Right side: Editable textarea */}
                <div className="w-full lg:flex-1 lg:w-auto relative">
                  {/* External label for Enhanced Output */}
                  <div className="flex items-center gap-2 mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                    <Sparkles className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                    <span>Enhanced Output</span>
                    <span className="text-xs text-gray-500 dark:text-gray-400">(Editable)</span>
                  </div>
                  <div className={`relative rounded-md ${
                    !isFocused
                      ? (isEnhancing || isTurboProcessing)
                        ? 'gradient-border-loading'
                        : 'bg-gradient-to-r from-cyan-400 via-blue-500 to-[#6E56CF] p-[2px]'
                      : ''
                  }`}>
                    <textarea
                      value={newTaskInput}
                      onChange={handleNewTaskInputChange}
                      onFocus={handleTextareaFocus}
                      onBlur={handleTextareaBlur}
                      placeholder={t('actions.newTask')}
                      style={{
                        height: `${textareaHeight}px`,
                        transition: hasManualResize ? 'none' : 'height 0.2s ease-in-out'
                      }}
                      className={`w-full px-4 py-3 pr-20 text-sm rounded-md bg-white dark:bg-[#1A1A1A] text-black dark:text-white placeholder:text-gray-400 dark:placeholder:text-gray-500 focus:outline-none resize-none overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent shadow-lg ${
                        isFocused
                          ? 'border border-[#5E6AD2] dark:border-[#6E56CF]'
                          : 'border-0'
                      }`}
                    />
                  </div>

                  {/* Button Group for dual layout */}
                  <div className={`absolute flex items-center gap-1 ${!isFocused ? 'right-1 bottom-1' : 'right-2 bottom-2'}`}>
                    {/* AI Turbo Button */}
                    <button
                      onClick={handleTurboMode}
                      disabled={!newTaskInput.trim() || isTurboProcessing || isEnhancing}
                      className="flex items-center justify-center w-8 h-8 text-[#5E6AD2] dark:text-[#6E56CF] hover:bg-[#5E6AD2]/10 dark:hover:bg-[#6E56CF]/10 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-[#5E6AD2]/20 dark:focus:ring-[#6E56CF]/20 disabled:text-gray-400 dark:disabled:text-gray-600 disabled:hover:bg-transparent"
                      title={isTurboProcessing ? turboProgressText || 'Processing...' : 'Turbo Mode - Enhance & Parse'}
                    >
                      <Zap className={`h-4 w-4 ${isTurboProcessing ? 'animate-pulse' : ''}`} />
                    </button>

                    {/* AI Enhancement Button */}
                    <button
                      onClick={handleAIEnhance}
                      disabled={!newTaskInput.trim() || isEnhancing || isTurboProcessing}
                      className="flex items-center justify-center w-8 h-8 text-[#5E6AD2] dark:text-[#6E56CF] hover:bg-[#5E6AD2]/10 dark:hover:bg-[#6E56CF]/10 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-[#5E6AD2]/20 dark:focus:ring-[#6E56CF]/20 disabled:text-gray-400 dark:disabled:text-gray-600 disabled:hover:bg-transparent"
                      title={isEnhancing ? 'Enhancing...' : t('actions.aiEnhance')}
                    >
                      <Sparkles className={`h-4 w-4 ${isEnhancing ? 'animate-pulse' : ''}`} />
                    </button>

                    {/* AI Thinking Toggle Button */}
                    {aiThinkingContent && (
                      <button
                        onClick={() => setShowThinking(!showThinking)}
                        className={`flex items-center justify-center w-8 h-8 transition-colors duration-200 focus:outline-none focus:ring-2 rounded-md ${
                          showThinking
                            ? 'text-purple-600 dark:text-purple-400 bg-purple-100 dark:bg-purple-900/30 hover:bg-purple-200 dark:hover:bg-purple-900/50 focus:ring-purple-500/20'
                            : 'text-[#5E6AD2] dark:text-[#6E56CF] hover:bg-[#5E6AD2]/10 dark:hover:bg-[#6E56CF]/10 focus:ring-[#5E6AD2]/20 dark:focus:ring-[#6E56CF]/20'
                        }`}
                        title={showThinking ? 'Hide AI reasoning' : 'Show AI reasoning'}
                      >
                        <Brain className="h-4 w-4" />
                      </button>
                    )}

                    {/* Send Button */}
                    <button
                      onClick={handleSubmitNewTask}
                      disabled={!newTaskInput.trim() || isEnhancing || isTurboProcessing}
                      className="flex items-center justify-center w-8 h-8 text-[#5E6AD2] dark:text-[#6E56CF] hover:bg-[#5E6AD2]/10 dark:hover:bg-[#6E56CF]/10 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-[#5E6AD2]/20 dark:focus:ring-[#6E56CF]/20 disabled:text-gray-400 dark:disabled:text-gray-600 disabled:hover:bg-transparent"
                      title={isEnhancing || isTurboProcessing ? 'Processing...' : t('actions.submitTask')}
                    >
                      <Send className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              /* Single textarea layout (default) */
              <div className={`relative rounded-md transition-all duration-300 ${
                !isFocused
                  ? (isEnhancing || isTurboProcessing)
                    ? 'gradient-border-loading'
                    : 'bg-gradient-to-r from-cyan-400 via-blue-500 to-[#6E56CF] p-[2px]'
                  : ''
              }`}>
                <textarea
                  value={newTaskInput}
                  onChange={handleNewTaskInputChange}
                  onFocus={handleTextareaFocus}
                  onBlur={handleTextareaBlur}
                  placeholder={t('actions.newTask')}
                  style={{
                    height: `${textareaHeight}px`,
                    transition: hasManualResize ? 'none' : 'height 0.2s ease-in-out'
                  }}
                  className={`w-full px-4 py-3 pr-20 text-sm rounded-md bg-white dark:bg-[#1A1A1A] text-black dark:text-white placeholder:text-gray-400 dark:placeholder:text-gray-500 focus:outline-none resize-none overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent shadow-lg ${
                    isFocused
                      ? 'border border-[#5E6AD2] dark:border-[#6E56CF]'
                      : 'border-0'
                  }`}
                />

                {/* Button Group for single layout */}
                <div className={`absolute flex items-center gap-1 ${!isFocused ? 'right-1 bottom-1' : 'right-2 bottom-2'}`}>
                  {/* AI Turbo Button */}
                  <button
                    onClick={handleTurboMode}
                    disabled={!newTaskInput.trim() || isTurboProcessing || isEnhancing}
                    className="flex items-center justify-center w-8 h-8 text-[#5E6AD2] dark:text-[#6E56CF] hover:bg-[#5E6AD2]/10 dark:hover:bg-[#6E56CF]/10 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-[#5E6AD2]/20 dark:focus:ring-[#6E56CF]/20 disabled:text-gray-400 dark:disabled:text-gray-600 disabled:hover:bg-transparent"
                    title={isTurboProcessing ? turboProgressText || 'Processing...' : 'Turbo Mode - Enhance & Parse'}
                  >
                    <Zap className={`h-4 w-4 ${isTurboProcessing ? 'animate-pulse' : ''}`} />
                  </button>

                  {/* AI Enhancement Button */}
                  <button
                    onClick={handleAIEnhance}
                    disabled={!newTaskInput.trim() || isEnhancing || isTurboProcessing}
                    className="flex items-center justify-center w-8 h-8 text-[#5E6AD2] dark:text-[#6E56CF] hover:bg-[#5E6AD2]/10 dark:hover:bg-[#6E56CF]/10 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-[#5E6AD2]/20 dark:focus:ring-[#6E56CF]/20 disabled:text-gray-400 dark:disabled:text-gray-600 disabled:hover:bg-transparent"
                    title={isEnhancing ? 'Enhancing...' : t('actions.aiEnhance')}
                  >
                    <Sparkles className={`h-4 w-4 ${isEnhancing ? 'animate-pulse' : ''}`} />
                  </button>

                  {/* AI Thinking Toggle Button */}
                  {aiThinkingContent && (
                    <button
                      onClick={() => setShowThinking(!showThinking)}
                      className={`flex items-center justify-center w-8 h-8 transition-colors duration-200 focus:outline-none focus:ring-2 rounded-md ${
                        showThinking
                          ? 'text-purple-600 dark:text-purple-400 bg-purple-100 dark:bg-purple-900/30 hover:bg-purple-200 dark:hover:bg-purple-900/50 focus:ring-purple-500/20'
                          : 'text-[#5E6AD2] dark:text-[#6E56CF] hover:bg-[#5E6AD2]/10 dark:hover:bg-[#6E56CF]/10 focus:ring-[#5E6AD2]/20 dark:focus:ring-[#6E56CF]/20'
                      }`}
                      title={showThinking ? 'Hide AI reasoning' : 'Show AI reasoning'}
                    >
                      <Brain className="h-4 w-4" />
                    </button>
                  )}

                  {/* Send Button */}
                  <button
                    onClick={handleSubmitNewTask}
                    disabled={!newTaskInput.trim() || isEnhancing || isTurboProcessing}
                    className="flex items-center justify-center w-8 h-8 text-[#5E6AD2] dark:text-[#6E56CF] hover:bg-[#5E6AD2]/10 dark:hover:bg-[#6E56CF]/10 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-[#5E6AD2]/20 dark:focus:ring-[#6E56CF]/20 disabled:text-gray-400 dark:disabled:text-gray-600 disabled:hover:bg-transparent"
                    title={isEnhancing || isTurboProcessing ? 'Processing...' : t('actions.submitTask')}
                  >
                    <Send className="h-4 w-4" />
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Project Flow Board Drawer */}
      {selectedTask && (
        <ProjectFlowBoard
          open={isProjectFlowBoardOpen}
          onOpenChange={setIsProjectFlowBoardOpen}
          task={selectedTask}
          onProjectDetailsModalOpen={handleProjectDetailsModalOpen}
          projectDescription={projectDescription}
          onProjectDescriptionChange={setProjectDescription}
          onCreateTask={createTask}
          onUpdateTask={updateTask}
          onDeleteTask={deleteTask}
          onFileUploaded={handleFileUploaded}
          onFileDeleted={handleFileDeleted}
          onTeamUpdated={handleTeamUpdated}
          onCommentAdded={refreshProjects}
          onOptimisticDescriptionUpdate={optimisticUpdateRef}
          onOptimisticStatusUpdate={handleOptimisticStatusUpdate}
          currentUserId={session?.user?.id}
          onParseContent={parseContentRef}
          isParsing={isParsing}
          onWorkspaceRefresh={refreshProjects}
        />
      )}

      {/* Project Details Modal - Root Level */}
      {isProjectDetailsModalOpen && (
        <div className="fixed inset-0 z-[10001] flex items-center justify-center">
          {/* Backdrop */}
          <div
            className="absolute inset-0 bg-black/50"
            onClick={handleCancelProjectDetails}
          />

          {/* Modal Card */}
          <div className="relative bg-white dark:bg-[#1A1A1A] rounded-lg shadow-2xl border border-gray-200 dark:border-[#2A2A2A] w-full max-w-3xl mx-4 max-h-[85vh] flex flex-col">
            {/* Header - Compact */}
            <div className="flex items-center justify-between px-4 py-3">
              <h2 className="text-base font-semibold text-black dark:text-white">
                {t('modal.projectDetails')}
              </h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCancelProjectDetails}
                className="h-7 w-7 p-0 hover:bg-gray-100 dark:hover:bg-[#2A2A2A]"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>

            {/* Content */}
            <div className="p-4 space-y-2">
              <textarea
                value={tempProjectDescription}
                onChange={(e) => setTempProjectDescription(e.target.value)}
                placeholder={canEditProjectDescription ? t('modal.placeholder') : 'You do not have permission to edit this project description'}
                readOnly={!canEditProjectDescription}
                className={`w-full min-h-[300px] p-3 text-xs border rounded-md transition-colors duration-200 resize-y ${
                  canEditProjectDescription
                    ? 'border-gray-200 dark:border-[#2A2A2A] bg-white dark:bg-[#1A1A1A] text-black dark:text-white placeholder:text-gray-400 dark:placeholder:text-gray-500 focus:outline-none focus:border-[#5E6AD2] dark:focus:border-[#6E56CF]'
                    : 'border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-800/50 text-gray-600 dark:text-gray-400 placeholder:text-gray-500 dark:placeholder:text-gray-600 cursor-not-allowed'
                }`}
                autoFocus={canEditProjectDescription}
              />

              {/* Permission indicator */}
              {!canEditProjectDescription && (
                <p className="text-xs text-gray-500 dark:text-gray-400 italic">
                  Only the project owner can edit the project description.
                </p>
              )}

              {/* Actions */}
              <div className="flex items-center justify-end gap-3">
                <Button
                  variant="outline"
                  onClick={handleCancelProjectDetails}
                  className="border-gray-200 dark:border-[#2A2A2A] hover:bg-gray-50 dark:hover:bg-[#2A2A2A]"
                >
                  {t('modal.cancel')}
                </Button>
                {canEditProjectDescription && (
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={handleParseContent}
                    disabled={isParsing || !tempProjectDescription.trim()}
                    className="border-[#5E6AD2] dark:border-[#6E56CF] text-[#5E6AD2] dark:text-[#6E56CF] hover:bg-[#5E6AD2]/10 dark:hover:bg-[#6E56CF]/10 disabled:opacity-50 disabled:cursor-not-allowed"
                    title={isParsing ? 'Parsing...' : 'Parse content'}
                  >
                    <FileText className="h-4 w-4" />
                  </Button>
                )}
                {canEditProjectDescription && (
                  <Button
                    onClick={handleSaveProjectDetails}
                    className="bg-[#5E6AD2] hover:bg-[#5E6AD2]/90 dark:bg-[#6E56CF] dark:hover:bg-[#6E56CF]/90"
                  >
                    {t('modal.saveChanges')}
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Trash Bin - Only visible during drag */}
      <TrashBin
        isVisible={isTaskDragging}
        onDeleteTask={handleDeleteTask}
      />

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        isOpen={deleteDialogOpen}
        task={taskToDelete}
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
        isDeleting={isDeleting}
      />

      {/* Card Animation Overlay */}
      <CardAnimationOverlay
        animation={currentAnimation}
        duration={animationConfig.duration}
        easing={animationConfig.easing}
      />

      {/* Confetti Animation for Task Completion */}
      {confettiConfig.isExploding && (
        <div
          style={{
            position: 'fixed',
            left: confettiConfig.position.x,
            top: confettiConfig.position.y,
            zIndex: 1000,
            pointerEvents: 'none'
          }}
        >
          <ConfettiExplosion
            force={0.5}
            duration={2600}
            particleCount={80}
            width={660}
            colors={[
              '#5E6AD2', '#6E56CF', // Purple theme colors
              '#FFD700', '#FFA500', // Gold and orange
              '#FF6B6B', '#FF7675', // Coral and red
              '#4ECDC4', '#00CED1', // Teal and turquoise
              '#45B7D1', '#87CEEB', // Blue shades
              '#96CEB4', '#98FB98', // Mint and light green
              '#FFEAA7', '#F0E68C', // Yellow shades
              '#DDA0DD', '#DA70D6'  // Plum and orchid
            ]}
          />
        </div>
      )}
      </div>
    </DndProvider>
  );
}
